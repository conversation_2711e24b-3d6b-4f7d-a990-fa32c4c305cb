import { Amplify } from 'aws-amplify';

export const cognitoConfig = {
  region: process.env.NEXT_PUBLIC_COGNITO_REGION!,
  userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID!,
  clientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID!,
  identityPoolId: process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID,
};

// Initialize AWS Amplify with Cognito configuration
export function initCognitoAuth() {
  const amplifyConfig = {
    Auth: {
      Cognito: {
        userPoolId: cognitoConfig.userPoolId,
        userPoolClientId: cognitoConfig.clientId,
        region: cognitoConfig.region,
        loginWith: {
          email: true,
          username: false
        }
      }
    }
  };

  // Add Identity Pool if provided
  if (cognitoConfig.identityPoolId) {
    Object.assign(amplifyConfig.Auth.Cognito, { identityPoolId: cognitoConfig.identityPoolId });
  }

  Amplify.configure(amplifyConfig);
}
