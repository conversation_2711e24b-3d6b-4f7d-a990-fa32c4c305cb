// Auth feature types
export interface AuthUser {
  userId: string
  username: string
  email?: string
}

export interface AuthResponse {
  success: boolean
  error?: string
}

export interface LoginResponse extends AuthResponse {
  user?: AuthUser
}

export interface SignupResponse extends AuthResponse {
  userId?: string
  nextStep?: {
    signUpStep: string
    codeDeliveryDetails?: {
      destination?: string
      deliveryMedium?: string
      attributeName?: string
    }
  }
}

export interface VerificationResponse extends AuthResponse {
  user?: AuthUser
}

// Auth context types
export interface AuthContextType {
  user: AuthUser | null
  isAuthenticated: boolean
  isLoading: boolean
  login: (email: string, password: string) => Promise<LoginResponse>
  logout: () => Promise<void>
  signup: (email: string, password: string, username?: string) => Promise<SignupResponse>
  verifyEmail: (email: string, code: string) => Promise<VerificationResponse>
  resendVerificationCode: (email: string) => Promise<AuthResponse>
  refreshUser: () => Promise<void>
}

// Form types
export interface LoginFormData {
  email: string
  password: string
  rememberMe?: boolean
}

export interface SignupFormData {
  email: string
  password: string
  confirmPassword: string
  username: string
}

export interface VerificationFormData {
  code: string
}

export interface ForgotPasswordFormData {
  email: string
}

export interface ResetPasswordFormData {
  code: string
  newPassword: string
  confirmPassword: string
}
