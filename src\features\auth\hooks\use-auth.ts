"use client"

import { useAuth as useAuthContext } from '@/contexts/auth/AuthContext'
import { useRouter, usePathname } from 'next/navigation'
import { useCallback, useEffect } from 'react'
import { loginUser, getAuthRedirectPath, authStorage } from '../utils'
import type { LoginResponse, SignupResponse, VerificationResponse, AuthResponse } from '../types'

export function useAuth() {
  const context = useAuthContext()
  const router = useRouter()
  const pathname = usePathname()

  // Auto-redirect based on auth state
  useEffect(() => {
    const redirectPath = getAuthRedirectPath(context.isAuthenticated, pathname)
    if (redirectPath) {
      router.push(redirectPath)
    }
  }, [context.isAuthenticated, pathname, router])

  const login = useCallback(async (email: string, password: string, rememberMe = false): Promise<LoginResponse> => {
    const result = await loginUser({ email, password })
    if (result.success) {
      await context.refreshUser()

      // Handle remember me functionality
      if (rememberMe) {
        authStorage.setRememberMe(true)
        authStorage.setLastEmail(email)
      } else {
        authStorage.clearRememberMe()
        authStorage.clearLastEmail()
      }
    }
    return result
  }, [context])

  const signOut = useCallback(async () => {
    await context.logout()
    // Clear remember me data on explicit sign out
    authStorage.clearRememberMe()
    authStorage.clearLastEmail()
    router.push('/login')
  }, [context, router])

  const signup = useCallback(async (email: string, password: string, username?: string): Promise<SignupResponse> => {
    return await context.signup(email, password, username)
  }, [context])

  const verifyEmail = useCallback(async (email: string, code: string): Promise<VerificationResponse> => {
    return await context.verifyEmail(email, code)
  }, [context])

  const resendVerificationCode = useCallback(async (email: string): Promise<AuthResponse> => {
    return await context.resendVerificationCode(email)
  }, [context])

  // Navigation helpers
  const goToLogin = useCallback(() => {
    router.push('/login')
  }, [router])

  const goToSignup = useCallback(() => {
    router.push('/signup')
  }, [router])

  const goToDashboard = useCallback(() => {
    router.push('/')
  }, [router])

  const goToForgotPassword = useCallback(() => {
    router.push('/forgot-password')
  }, [router])

  // Utility functions
  const getLastEmail = useCallback(() => {
    return authStorage.getLastEmail()
  }, [])

  const getRememberMe = useCallback(() => {
    return authStorage.getRememberMe()
  }, [])

  return {
    // Auth state
    user: context.user,
    isLoading: context.isLoading,
    isAuthenticated: context.isAuthenticated,

    // Auth actions
    login,
    signup,
    signOut,
    verifyEmail,
    resendVerificationCode,
    refreshUser: context.refreshUser,

    // Navigation helpers
    goToLogin,
    goToSignup,
    goToDashboard,
    goToForgotPassword,

    // Utility functions
    getLastEmail,
    getRememberMe,
  }
}
