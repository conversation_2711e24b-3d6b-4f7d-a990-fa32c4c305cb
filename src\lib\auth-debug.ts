import { Amplify } from 'aws-amplify';
import { cognitoConfig } from './cognito';

/**
 * Debug utility to help diagnose authentication issues
 */
export function debugAuthConfiguration() {
  console.group('🔍 Authentication Debug Information');
  
  // Environment variables
  console.log('📋 Environment Variables:');
  console.log('  NEXT_PUBLIC_COGNITO_REGION:', process.env.NEXT_PUBLIC_COGNITO_REGION);
  console.log('  NEXT_PUBLIC_COGNITO_USER_POOL_ID:', process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID);
  console.log('  NEXT_PUBLIC_COGNITO_CLIENT_ID:', process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID);
  console.log('  NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID:', process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID);
  
  // Parsed configuration
  console.log('⚙️ Parsed Configuration:');
  console.log('  Region:', cognitoConfig.region);
  console.log('  User Pool ID:', cognitoConfig.userPoolId);
  console.log('  Client ID:', cognitoConfig.clientId);
  console.log('  Identity Pool ID:', cognitoConfig.identityPoolId);
  
  // AWS Amplify configuration
  try {
    const amplifyConfig = Amplify.getConfig();
    console.log('🔧 AWS Amplify Configuration:');
    console.log('  Auth Config:', amplifyConfig.Auth);
  } catch (error) {
    console.log('❌ Failed to get Amplify config:', error);
  }
  
  // Validation checks
  console.log('✅ Validation Checks:');
  const checks = [
    {
      name: 'User Pool ID format',
      valid: /^[a-z0-9-]+_[a-zA-Z0-9]+$/.test(cognitoConfig.userPoolId),
      value: cognitoConfig.userPoolId
    },
    {
      name: 'Client ID format',
      valid: /^[a-z0-9]+$/.test(cognitoConfig.clientId),
      value: cognitoConfig.clientId
    },
    {
      name: 'Region format',
      valid: /^[a-z0-9-]+$/.test(cognitoConfig.region),
      value: cognitoConfig.region
    }
  ];
  
  checks.forEach(check => {
    console.log(`  ${check.valid ? '✅' : '❌'} ${check.name}: ${check.value}`);
  });
  
  console.groupEnd();
}

/**
 * Test authentication configuration without making actual login attempt
 */
export async function testAuthConfiguration() {
  console.group('🧪 Testing Authentication Configuration');
  
  try {
    // Test if AWS Amplify is properly configured
    const config = Amplify.getConfig();

    if (!config.Auth?.Cognito) {
      throw new Error('AWS Amplify Auth.Cognito configuration is missing');
    }

    const cognitoAuth = config.Auth.Cognito;

    console.log('✅ AWS Amplify is configured');
    console.log('📋 Active Configuration:');
    console.log('  User Pool ID:', 'userPoolId' in cognitoAuth ? cognitoAuth.userPoolId : 'N/A');
    console.log('  Client ID:', 'userPoolClientId' in cognitoAuth ? cognitoAuth.userPoolClientId : 'N/A');
    console.log('  Region:', 'region' in cognitoAuth ? cognitoAuth.region : 'N/A');
    console.log('  Login With:', 'loginWith' in cognitoAuth ? cognitoAuth.loginWith : 'N/A');

    // Validate required fields
    const requiredFields = ['userPoolId', 'userPoolClientId'];
    const missingFields = requiredFields.filter(field => !(field in cognitoAuth) || !cognitoAuth[field as keyof typeof cognitoAuth]);

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
    }

    console.log('✅ All required configuration fields are present');

    return {
      success: true,
      config: cognitoAuth
    };
    
  } catch (error) {
    console.error('❌ Configuration test failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  } finally {
    console.groupEnd();
  }
}

/**
 * Log detailed error information for debugging
 */
export function logAuthError(error: unknown, context: string) {
  console.group(`❌ Authentication Error - ${context}`);

  if (error instanceof Error) {
    console.log('Error Name:', error.name);
    console.log('Error Message:', error.message);
    console.log('Error Stack:', error.stack);

    // Check for specific AWS error properties
    const errorWithCode = error as Error & { code?: string };
    if ('code' in error) {
      console.log('AWS Error Code:', errorWithCode.code);
    }

    const errorWithStatus = error as Error & { statusCode?: number };
    if ('statusCode' in error) {
      console.log('Status Code:', errorWithStatus.statusCode);
    }

    const errorWithRetry = error as Error & { retryable?: boolean };
    if ('retryable' in error) {
      console.log('Retryable:', errorWithRetry.retryable);
    }
  } else {
    console.log('Non-Error object:', error);
  }

  console.groupEnd();
}
