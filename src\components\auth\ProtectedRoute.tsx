"use client"

import { useEffect, type ReactNode } from "react"
import { useAuth } from "@/hooks/use-auth"
import { Loader2 } from "lucide-react"

interface ProtectedRouteProps {
  children: ReactNode
  fallback?: ReactNode
  requireAuth?: boolean
  redirectTo?: string
}

export function ProtectedRoute({ 
  children, 
  fallback,
  requireAuth = true,
  redirectTo 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, goToLogin, goToDashboard } = useAuth()

  useEffect(() => {
    if (!isLoading) {
      if (requireAuth && !isAuthenticated) {
        // Redirect to login if authentication is required but user is not authenticated
        if (redirectTo) {
          window.location.href = redirectTo
        } else {
          goToLogin()
        }
      } else if (!requireAuth && isAuthenticated) {
        // Redirect to dashboard if user is authenticated but on a public route
        if (redirectTo) {
          window.location.href = redirectTo
        } else {
          goToDashboard()
        }
      }
    }
  }, [isAuthenticated, isLoading, requireAuth, redirectTo, goToLogin, goToDashboard])

  // Show loading state while checking authentication
  if (isLoading) {
    return fallback || (
      <div className="flex h-screen w-screen items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <h2 className="text-xl font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Please wait while we verify your session</p>
        </div>
      </div>
    )
  }

  // Show children if auth requirements are met
  if ((requireAuth && isAuthenticated) || (!requireAuth && !isAuthenticated)) {
    return <>{children}</>
  }

  // Return null while redirecting
  return null
}

// Convenience components for common use cases
export function AuthenticatedRoute({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <ProtectedRoute requireAuth={true} fallback={fallback}>
      {children}
    </ProtectedRoute>
  )
}

export function PublicRoute({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <ProtectedRoute requireAuth={false} fallback={fallback}>
      {children}
    </ProtectedRoute>
  )
}
