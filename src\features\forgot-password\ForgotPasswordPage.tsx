"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { forgotPassword, resetPassword } from "@/lib/auth-utils"
import { Loader2, ArrowLeft, CheckCircle } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"

const requestResetSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
})

const resetPasswordSchema = z.object({
  email: z.string().email(),
  code: z.string().min(6, "Verification code must be at least 6 characters"),
  newPassword: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number"),
  confirmPassword: z.string(),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

type RequestResetValues = z.infer<typeof requestResetSchema>
type ResetPasswordValues = z.infer<typeof resetPasswordSchema>

type ForgotPasswordStep = "request" | "reset" | "success"

export default function ForgotPasswordPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState<ForgotPasswordStep>("request")
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [email, setEmail] = useState("")

  const requestResetForm = useForm<RequestResetValues>({
    resolver: zodResolver(requestResetSchema),
    defaultValues: { email: "" },
  })

  const resetPasswordForm = useForm<ResetPasswordValues>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: {
      email: "",
      code: "",
      newPassword: "",
      confirmPassword: "",
    },
  })

  async function onRequestReset(values: RequestResetValues) {
    setIsLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      const result = await forgotPassword(values.email)
      if (result.success) {
        setEmail(values.email)
        resetPasswordForm.setValue("email", values.email)
        setSuccess("Verification code has been sent to your email")
        setCurrentStep("reset")
      } else {
        setError(result.error || "An error occurred")
      }
    } catch {
      setError("An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  async function onResetPassword(values: ResetPasswordValues) {
    setIsLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      const result = await resetPassword(values.email, values.code, values.newPassword)
      if (result.success) {
        setCurrentStep("success")
      } else {
        setError(result.error || "An error occurred")
      }
    } catch {
      setError("An unexpected error occurred")
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToRequest = () => {
    setCurrentStep("request")
    setError(null)
    setSuccess(null)
    setEmail("")
    resetPasswordForm.reset()
  }

  const handleGoToLogin = () => {
    router.push("/login")
  }

  const getStepContent = () => {
    switch (currentStep) {
      case "request":
        return {
          title: "Reset Password",
          description: "Enter your email to receive a reset code",
          content: (
            <Form {...requestResetForm}>
              <form onSubmit={requestResetForm.handleSubmit(onRequestReset)} className="space-y-4">
                <FormField
                  control={requestResetForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your email" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Sending reset code...
                    </>
                  ) : (
                    "Send Reset Code"
                  )}
                </Button>
              </form>
            </Form>
          )
        }

      case "reset":
        return {
          title: "Enter Reset Code",
          description: `Enter the code sent to ${email}`,
          content: (
            <Form {...resetPasswordForm}>
              <form onSubmit={resetPasswordForm.handleSubmit(onResetPassword)} className="space-y-4">
                <FormField
                  control={resetPasswordForm.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input {...field} disabled />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={resetPasswordForm.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Verification Code</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter verification code" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={resetPasswordForm.control}
                  name="newPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Enter new password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={resetPasswordForm.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Confirm New Password</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Confirm new password" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="submit" className="w-full" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Resetting password...
                    </>
                  ) : (
                    "Reset Password"
                  )}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleBackToRequest}
                  className="w-full"
                >
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back to Email
                </Button>
              </form>
            </Form>
          )
        }

      case "success":
        return {
          title: "Password Reset Successful",
          description: "Your password has been reset successfully",
          content: (
            <div className="text-center space-y-4">
              <div className="mx-auto w-12 h-12 bg-green-500/10 rounded-full flex items-center justify-center">
                <CheckCircle className="w-6 h-6 text-green-500" />
              </div>
              <p className="text-sm text-muted-foreground">
                You can now sign in with your new password.
              </p>
              <Button onClick={handleGoToLogin} className="w-full">
                Go to Login
              </Button>
            </div>
          )
        }
    }
  }

  const stepContent = getStepContent()

  return (
    <div className="w-full max-w-md space-y-6">
      <Card>
        <CardHeader className="text-center">
          <CardTitle>{stepContent.title}</CardTitle>
          <CardDescription>{stepContent.description}</CardDescription>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="bg-destructive/15 text-destructive text-sm p-3 rounded-md mb-6">
              {error}
            </div>
          )}
          {success && (
            <div className="bg-green-500/15 text-green-500 text-sm p-3 rounded-md mb-6">
              {success}
            </div>
          )}
          
          {stepContent.content}

          {currentStep === "request" && (
            <div className="mt-6 text-center">
              <Link href="/login" className="text-sm text-muted-foreground hover:text-primary">
                Remember your password? Sign in
              </Link>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
