"use client"

import { createContext, useContext, useEffect, useState, type ReactNode } from "react"
import {
  getCurrentAuthUser,
  logoutUser,
  signupUser,
  verifyEmail,
  resendVerificationCode,
  type AuthUser,
  type SignupResponse,
  type VerificationResponse,
  type AuthResponse
} from "@/lib/auth-utils"
import { initCognitoAuth } from "@/lib/cognito"
import { debugAuthConfiguration, testAuthConfiguration, logAuthError } from "@/lib/auth-debug"

interface AuthContextType {
  user: AuthUser | null
  isLoading: boolean
  isAuthenticated: boolean
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
  signup: (email: string, password: string, username?: string) => Promise<SignupResponse>
  verifyUserEmail: (email: string, code: string) => Promise<VerificationResponse>
  resendCode: (email: string) => Promise<AuthResponse>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const isAuthenticated = !!user

  // Initialize Amplify and check for existing session
  useEffect(() => {
    console.log('🚀 Initializing Cognito Auth...')

    const initializeAuth = async () => {
      try {
        // Debug configuration in development
        if (process.env.NODE_ENV === 'development') {
          debugAuthConfiguration()
        }

        // Initialize Cognito Auth
        const initResult = initCognitoAuth()
        console.log('🔧 Init result:', initResult)

        // Small delay to ensure Amplify is fully configured
        await new Promise(resolve => setTimeout(resolve, 100))

        // Test configuration
        const testResult = await testAuthConfiguration()
        if (testResult.success) {
          console.log('✅ Auth configuration test passed')
          await checkAuthState()
        } else {
          console.error('❌ Auth configuration test failed:', testResult.error)
          setIsLoading(false)
        }

      } catch (error) {
        logAuthError(error, 'Auth Initialization')
        setIsLoading(false)
      }
    }

    initializeAuth()
  }, [])

  async function checkAuthState() {
    try {
      setIsLoading(true)
      console.log('🔍 Checking authentication state...')

      const currentUser = await getCurrentAuthUser()

      if (currentUser) {
        console.log('✅ User is authenticated:', currentUser.username)
        setUser(currentUser)
      } else {
        console.log('ℹ️ No authenticated user found')
        setUser(null)
      }
    } catch (error) {
      logAuthError(error, 'Auth State Check')
      setUser(null)
    } finally {
      setIsLoading(false)
    }
  }

  async function signOut() {
    try {
      setIsLoading(true)
      await logoutUser()
      setUser(null)
    } catch (error) {
      console.error("Sign out error:", error)
    } finally {
      setIsLoading(false)
    }
  }

  async function refreshUser() {
    try {
      const currentUser = await getCurrentAuthUser()
      setUser(currentUser)
    } catch (error) {
      console.error("Refresh user error:", error)
      setUser(null)
    }
  }

  async function signup(email: string, password: string, username?: string): Promise<SignupResponse> {
    return await signupUser({ email, password, username })
  }

  async function verifyUserEmail(email: string, code: string): Promise<VerificationResponse> {
    const result = await verifyEmail({ email, code })
    if (result.success && result.user) {
      setUser(result.user)
    }
    return result
  }

  async function resendCode(email: string): Promise<AuthResponse> {
    return await resendVerificationCode(email)
  }

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    signOut,
    refreshUser,
    signup,
    verifyUserEmail,
    resendCode,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}
