import { Amplify } from 'aws-amplify';

export const cognitoConfig = {
  region: process.env.NEXT_PUBLIC_COGNITO_REGION || "us-east-2",
  userPoolId: process.env.NEXT_PUBLIC_COGNITO_USER_POOL_ID || "us-east-2_YourUserPoolId",
  clientId: process.env.NEXT_PUBLIC_COGNITO_CLIENT_ID || "your-client-id",
  // Optional: Only needed if using Identity Pool for additional AWS services
  identityPoolId: process.env.NEXT_PUBLIC_COGNITO_IDENTITY_POOL_ID,
};

// Validate configuration
function validateConfig() {
  console.log('🔍 Validating Cognito configuration...');

  // Check if we have the actual values (not defaults)
  const hasValidUserPoolId = cognitoConfig.userPoolId && cognitoConfig.userPoolId !== "us-east-2_YourUserPoolId";
  const hasValidClientId = cognitoConfig.clientId && cognitoConfig.clientId !== "your-client-id";
  const hasValidRegion = cognitoConfig.region && cognitoConfig.region !== "";

  if (!hasValidUserPoolId) {
    console.error('❌ Invalid or missing User Pool ID:', cognitoConfig.userPoolId);
    throw new Error('User Pool ID is not configured properly');
  }

  if (!hasValidClientId) {
    console.error('❌ Invalid or missing Client ID:', cognitoConfig.clientId);
    throw new Error('Client ID is not configured properly');
  }

  if (!hasValidRegion) {
    console.error('❌ Invalid or missing Region:', cognitoConfig.region);
    throw new Error('Region is not configured properly');
  }

  // Validate format
  if (!cognitoConfig.userPoolId.match(/^[a-z0-9-]+_[a-zA-Z0-9]+$/)) {
    console.error('❌ Invalid User Pool ID format:', cognitoConfig.userPoolId);
    throw new Error('Invalid User Pool ID format');
  }

  console.log('✅ Cognito configuration validated successfully');
}

// Initialize AWS Amplify with Cognito configuration
export function initCognitoAuth() {
  try {
    console.log('🚀 Starting AWS Amplify initialization...');

    // Validate configuration first
    validateConfig();

    const amplifyConfig = {
      Auth: {
        Cognito: {
          userPoolId: cognitoConfig.userPoolId,
          userPoolClientId: cognitoConfig.clientId,
          region: cognitoConfig.region,
          // Configure to use email as username (this was the key!)
          loginWith: {
            email: true,
            username: false
          }
        }
      }
    };

    // Add Identity Pool if provided
    if (cognitoConfig.identityPoolId && cognitoConfig.identityPoolId !== 'us-east-2:xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx') {
      Object.assign(amplifyConfig.Auth.Cognito, { identityPoolId: cognitoConfig.identityPoolId });
      console.log('🔧 Added Identity Pool ID to config');
    }

    console.log('🔧 Configuring AWS Amplify with:', {
      userPoolId: amplifyConfig.Auth.Cognito.userPoolId,
      userPoolClientId: amplifyConfig.Auth.Cognito.userPoolClientId,
      region: amplifyConfig.Auth.Cognito.region,
      loginWith: amplifyConfig.Auth.Cognito.loginWith
    });

    Amplify.configure(amplifyConfig);

    console.log('✅ AWS Amplify configured successfully');

    return true;

  } catch (error) {
    console.error('❌ Failed to initialize AWS Amplify:', error);
    console.error('❌ Error details:', {
      name: error instanceof Error ? error.name : 'Unknown',
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error;
  }
}
