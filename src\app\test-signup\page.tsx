"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { signupUser, verifyEmail, resendVerificationCode } from "@/lib/auth-utils"

export default function TestSignupPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [code, setCode] = useState("")
  const [output, setOutput] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const addOutput = (message: string) => {
    setOutput(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const testSignup = async () => {
    if (!email || !password) {
      addOutput("❌ Please enter email and password")
      return
    }

    setIsLoading(true)
    addOutput(`🔐 Testing signup for: ${email}`)

    try {
      const result = await signupUser({
        email,
        password,
        username: "testuser"
      })

      addOutput(`📊 Signup result: ${JSON.stringify(result, null, 2)}`)

      if (result.success) {
        addOutput("✅ Signup successful!")
        if (result.nextStep?.signUpStep === "CONFIRM_SIGN_UP") {
          addOutput("📧 Email verification required")
        }
      } else {
        addOutput(`❌ Signup failed: ${result.error}`)
      }
    } catch (error) {
      addOutput(`❌ Signup error: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testVerification = async () => {
    if (!email || !code) {
      addOutput("❌ Please enter email and verification code")
      return
    }

    setIsLoading(true)
    addOutput(`🔐 Testing verification for: ${email}`)

    try {
      const result = await verifyEmail({ email, code })
      addOutput(`📊 Verification result: ${JSON.stringify(result, null, 2)}`)

      if (result.success) {
        addOutput("✅ Verification successful!")
      } else {
        addOutput(`❌ Verification failed: ${result.error}`)
      }
    } catch (error) {
      addOutput(`❌ Verification error: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setIsLoading(false)
    }
  }

  const testResendCode = async () => {
    if (!email) {
      addOutput("❌ Please enter email")
      return
    }

    setIsLoading(true)
    addOutput(`🔐 Testing resend code for: ${email}`)

    try {
      const result = await resendVerificationCode(email)
      addOutput(`📊 Resend result: ${JSON.stringify(result, null, 2)}`)

      if (result.success) {
        addOutput("✅ Code resent successfully!")
      } else {
        addOutput(`❌ Resend failed: ${result.error}`)
      }
    } catch (error) {
      addOutput(`❌ Resend error: ${error instanceof Error ? error.message : String(error)}`)
    } finally {
      setIsLoading(false)
    }
  }

  const clearOutput = () => {
    setOutput([])
  }

  return (
    <div className="container mx-auto p-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Signup Flow Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Test Inputs</h3>
              
              <div>
                <label className="block text-sm font-medium mb-1">Email</label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Password</label>
                <Input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="Password123!"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Verification Code</label>
                <Input
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  placeholder="123456"
                />
              </div>

              <div className="space-y-2">
                <Button onClick={testSignup} disabled={isLoading} className="w-full">
                  Test Signup
                </Button>
                <Button onClick={testVerification} disabled={isLoading} className="w-full" variant="outline">
                  Test Verification
                </Button>
                <Button onClick={testResendCode} disabled={isLoading} className="w-full" variant="outline">
                  Test Resend Code
                </Button>
                <Button onClick={clearOutput} variant="ghost" className="w-full">
                  Clear Output
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Test Output</h3>
                <span className="text-sm text-muted-foreground">
                  {output.length} messages
                </span>
              </div>
              
              <div className="bg-muted p-4 rounded-md h-96 overflow-y-auto">
                {output.length === 0 ? (
                  <p className="text-muted-foreground text-sm">No output yet...</p>
                ) : (
                  <div className="space-y-1">
                    {output.map((line, index) => (
                      <div key={index} className="text-sm font-mono">
                        {line}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
