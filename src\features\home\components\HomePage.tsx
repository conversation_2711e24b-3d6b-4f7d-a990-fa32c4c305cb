'use client';

import { useTranslations } from 'next-intl';
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import ThemeToggle from "@/components/shared/theme/ThemeToggle";
import LanguageToggle from "@/components/shared/language/LanguageToggle";
import { Album, Briefcase, PlayCircle, Send, User, LogIn, LogOut } from "lucide-react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/use-auth";

export default function HomePage() {
  const t = useTranslations('HomePage');
  const router = useRouter();
  const { isAuthenticated, user, signOut, goToLogin, goToSignup } = useAuth();

  const handleArtistClick = () => {
    router.push(`/artist`);
  };
  const handleAlbumClick = () => {
    router.push(`/album`);
  };
  const handleOpportunityClick = () => {
    router.push(`/opportunities`);
  };
  const handleApplicationClick = () => {
    router.push(`/applications`);
  };
  const handlePlaylistClick = () => {
    router.push(`/playlist`);
  };

  const handleSignOut = async () => {
    await signOut();
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Header */}
      <header className="flex justify-between items-center p-6">
        <div className="flex items-center gap-4">
          <h2 className="text-2xl font-bold">Smash Music</h2>
        </div>
        <div className="flex items-center gap-3">
          {isAuthenticated ? (
            <div className="flex items-center gap-3">
              <span className="text-sm text-muted-foreground">
                Welcome, {user?.email || user?.username}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                className="gap-2"
              >
                <LogOut className="w-4 h-4" />
                Sign Out
              </Button>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={goToLogin}
                className="gap-2"
              >
                <LogIn className="w-4 h-4" />
                Sign In
              </Button>
              <Button
                size="sm"
                onClick={goToSignup}
                className="gap-2"
              >
                <User className="w-4 h-4" />
                Sign Up
              </Button>
            </div>
          )}
          <LanguageToggle />
          <ThemeToggle />
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            {t('title')}
          </h1>
          
          <p className="text-xl text-muted-foreground mb-12 max-w-2xl mx-auto">
            {t('subtitle')}
          </p>

          {/* Feature Cards */}
<div className="grid md:grid-cols-3 gap-6 mb-12">
  <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handlePlaylistClick()}>
    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
      <PlayCircle />
    </div>
    <h3 className="text-lg font-semibold mb-2">Playlist</h3>
    <p className="text-muted-foreground">Create and enjoy personalized playlists curated for your taste.</p>
  </Card>

  <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleArtistClick()}>
    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
      <User />
    </div>
    <h3 className="text-lg font-semibold mb-2">Artist</h3>
    <p className="text-muted-foreground">Discover top artists, explore their profiles, and follow your favorites.</p>
  </Card>

  <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleAlbumClick()}>
    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
      <Album />
    </div>
    <h3 className="text-lg font-semibold mb-2">Album</h3>
    <p className="text-muted-foreground">Browse and listen to curated albums across various genres.</p>
  </Card>

  <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleOpportunityClick()}>
    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
      <Briefcase />
    </div>
    <h3 className="text-lg font-semibold mb-2">Opportunities</h3>
    <p className="text-muted-foreground">Explore the latest career openings and project opportunities.</p>
  </Card>

  <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleApplicationClick()}>
    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
      <Send />
    </div>
    <h3 className="text-lg font-semibold mb-2">My Applications</h3>
    <p className="text-muted-foreground">Track the status and progress of your submitted job applications.</p>
  </Card>
</div>


       
        </div>
      </main>

    </div>
  );
}
