"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"

export interface Song {
  id: string
  title: string
  artist: string
  album: string
  albumArt: string
  duration: number // in seconds
  audioSrc: string
  credits?: {
    producer?: string
    writer?: string
    engineer?: string
  }
}

interface MusicPlayerContextType {
  currentSong: Song | null
  isPlaying: boolean
  volume: number
  progress: number
  duration: number
  isExpanded: boolean
  playSong: (song: Song) => void
  togglePlay: () => void
  setVolume: (volume: number) => void
  seekTo: (time: number) => void
  nextSong: () => void
  prevSong: () => void
  toggleExpanded: () => void
}

const MusicPlayerContext = createContext<MusicPlayerContextType | undefined>(undefined)

// Sample songs data
const sampleSongs: Song[] = [
  {
    id: "1",
    title: "Electric Dreams",
    artist: "<PERSON>",
    album: "Midnight Echoes",
    albumArt: "/placeholder.svg?height=300&width=300",
    duration: 259, // 4:19
    audioSrc: "/sample-audio.mp3",
    credits: {
      producer: "<PERSON>",
      writer: "<PERSON>, <PERSON>",
      engineer: "<PERSON>",
    },
  },
  {
    id: "2",
    title: "<PERSON>n Nights",
    artist: "<PERSON>",
    album: "Urban Landscapes",
    albumArt: "/placeholder.svg?height=300&width=300",
    duration: 222, // 3:42
    audioSrc: "/sample-audio.mp3",
    credits: {
      producer: "Sarah Mitchell",
      writer: "Sarah Mitchell",
      engineer: "Emma <PERSON>",
    },
  },
  {
    id: "3",
    title: "City Lights",
    artist: "The Bridges",
    album: "Metropolitan",
    albumArt: "/placeholder.svg?height=300&width=300",
    duration: 315, // 5:15
    audioSrc: "/sample-audio.mp3",
    credits: {
      producer: "Mike Rodriguez",
      writer: "The Bridges",
      engineer: "Alex Thompson",
    },
  },
]

export const MusicPlayerProvider = ({ children }: { children: ReactNode }) => {
  const [currentSong, setCurrentSong] = useState<Song | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [volume, setVolume] = useState(0.7)
  const [progress, setProgress] = useState(0)
  const [duration, setDuration] = useState(0)
  const [isExpanded, setIsExpanded] = useState(false)
  const [audioElement, setAudioElement] = useState<HTMLAudioElement | null>(null)
  const [currentSongIndex, setCurrentSongIndex] = useState(0)

  // Initialize audio element
  useEffect(() => {
    const audio = new Audio()
    setAudioElement(audio)

    return () => {
      audio.pause()
      audio.src = ""
    }
  }, [])

  // Update audio when current song changes
  useEffect(() => {
    if (audioElement && currentSong) {
      audioElement.src = currentSong.audioSrc
      audioElement.volume = volume
      setDuration(currentSong.duration)

      if (isPlaying) {
        audioElement.play().catch((error) => {
          console.error("Error playing audio:", error)
          setIsPlaying(false)
        })
      }
    }
  }, [audioElement, currentSong])

  // Update progress
  useEffect(() => {
    if (!audioElement) return

    const updateProgress = () => {
      setProgress(audioElement.currentTime)
    }

    const interval = setInterval(updateProgress, 1000)
    audioElement.addEventListener("ended", handleSongEnd)

    return () => {
      clearInterval(interval)
      audioElement.removeEventListener("ended", handleSongEnd)
    }
  }, [audioElement])

  // Handle song end
  const handleSongEnd = () => {
    nextSong()
  }

  // Play a specific song
  const playSong = (song: Song) => {
    const songIndex = sampleSongs.findIndex((s) => s.id === song.id)
    if (songIndex !== -1) {
      setCurrentSongIndex(songIndex)
    }

    setCurrentSong(song)
    setIsPlaying(true)
  }

  // Toggle play/pause
  const togglePlay = () => {
    if (!currentSong) {
      // If no song is selected, play the first one
      if (sampleSongs.length > 0) {
        playSong(sampleSongs[0])
      }
      return
    }

    if (audioElement) {
      if (isPlaying) {
        audioElement.pause()
      } else {
        audioElement.play().catch((error) => {
          console.error("Error playing audio:", error)
        })
      }
      setIsPlaying(!isPlaying)
    }
  }

  // Set volume
  const setVolumeHandler = (newVolume: number) => {
    if (audioElement) {
      audioElement.volume = newVolume
    }
    setVolume(newVolume)
  }

  // Seek to a specific time
  const seekTo = (time: number) => {
    if (audioElement) {
      audioElement.currentTime = time
      setProgress(time)
    }
  }

  // Play next song
  const nextSong = () => {
    if (sampleSongs.length === 0) return

    const nextIndex = (currentSongIndex + 1) % sampleSongs.length
    setCurrentSongIndex(nextIndex)
    playSong(sampleSongs[nextIndex])
  }

  // Play previous song
  const prevSong = () => {
    if (sampleSongs.length === 0) return

    const prevIndex = (currentSongIndex - 1 + sampleSongs.length) % sampleSongs.length
    setCurrentSongIndex(prevIndex)
    playSong(sampleSongs[prevIndex])
  }

  // Toggle expanded view
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  const value = {
    currentSong,
    isPlaying,
    volume,
    progress,
    duration,
    isExpanded,
    playSong,
    togglePlay,
    setVolume: setVolumeHandler,
    seekTo,
    nextSong,
    prevSong,
    toggleExpanded,
  }

  return <MusicPlayerContext.Provider value={value}>{children}</MusicPlayerContext.Provider>
}

export const useMusicPlayer = () => {
  const context = useContext(MusicPlayerContext)
  if (context === undefined) {
    throw new Error("useMusicPlayer must be used within a MusicPlayerProvider")
  }
  return context
}
