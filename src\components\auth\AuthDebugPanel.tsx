"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { debugAuthConfiguration, testAuthConfiguration, logAuthError } from "@/lib/auth-debug"
import { loginUser } from "@/lib/auth-utils"
import { Loader2, Bug, TestTube, Eye, EyeOff } from "lucide-react"

export function AuthDebugPanel() {
  const [isLoading, setIsLoading] = useState(false)
  const [testEmail, setTestEmail] = useState("")
  const [testPassword, setTestPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [debugOutput, setDebugOutput] = useState("")

  const addToOutput = (message: string) => {
    setDebugOutput(prev => prev + message + "\n")
  }

  const clearOutput = () => {
    setDebugOutput("")
  }

  const runConfigTest = async () => {
    clearOutput()
    addToOutput("=== Configuration Test ===")
    
    // Capture console output
    const originalLog = console.log
    const originalError = console.error
    
    console.log = (...args) => {
      addToOutput(args.join(" "))
      originalLog(...args)
    }
    
    console.error = (...args) => {
      addToOutput("ERROR: " + args.join(" "))
      originalError(...args)
    }

    try {
      debugAuthConfiguration()
      const result = await testAuthConfiguration()
      
      if (result.success) {
        addToOutput("✅ Configuration test PASSED")
      } else {
        addToOutput("❌ Configuration test FAILED: " + result.error)
      }
    } catch (error) {
      addToOutput("❌ Configuration test ERROR: " + (error instanceof Error ? error.message : String(error)))
    } finally {
      // Restore console
      console.log = originalLog
      console.error = originalError
    }
  }



  const runLoginTest = async () => {
    if (!testEmail || !testPassword) {
      addToOutput("❌ Please enter both email and password")
      return
    }

    setIsLoading(true)
    addToOutput("\n=== Login Test ===")
    addToOutput(`Testing login for: ${testEmail}`)

    // Capture console output
    const originalLog = console.log
    const originalError = console.error

    console.log = (...args) => {
      addToOutput(args.join(" "))
      originalLog(...args)
    }

    console.error = (...args) => {
      addToOutput("ERROR: " + args.join(" "))
      originalError(...args)
    }

    try {
      const result = await loginUser({
        email: testEmail,
        password: testPassword
      })

      if (result.success) {
        addToOutput("✅ Login test PASSED")
        addToOutput(`User: ${JSON.stringify(result.user, null, 2)}`)
      } else {
        addToOutput("❌ Login test FAILED: " + result.error)
      }
    } catch (error) {
      addToOutput("❌ Login test ERROR: " + (error instanceof Error ? error.message : String(error)))
      logAuthError(error, "Login Test")
    } finally {
      // Restore console
      console.log = originalLog
      console.error = originalError
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bug className="w-5 h-5" />
            Authentication Debug Panel
          </CardTitle>
          <CardDescription>
            Debug and test AWS Amplify authentication configuration
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={runConfigTest} variant="outline">
              <TestTube className="w-4 h-4 mr-2" />
              Test Configuration
            </Button>
            <Button onClick={clearOutput} variant="outline">
              Clear Output
            </Button>
          </div>

          <div className="space-y-4 border-t pt-4">
            <h3 className="font-medium">Test Login</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="test-email">Email</Label>
                <Input
                  id="test-email"
                  type="email"
                  value={testEmail}
                  onChange={(e) => setTestEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="test-password">Password</Label>
                <div className="relative">
                  <Input
                    id="test-password"
                    type={showPassword ? "text" : "password"}
                    value={testPassword}
                    onChange={(e) => setTestPassword(e.target.value)}
                    placeholder="Enter password"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </div>
            <Button onClick={runLoginTest} disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Testing...
                </>
              ) : (
                "Test Login"
              )}
            </Button>
          </div>

          <div className="space-y-2">
            <Label htmlFor="debug-output">Debug Output</Label>
            <Textarea
              id="debug-output"
              value={debugOutput}
              readOnly
              className="min-h-[300px] font-mono text-sm"
              placeholder="Debug output will appear here..."
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
